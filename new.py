import mysql.connector
import uuid
import hashlib
from pymediainfo import MediaInfo
from shutil import copyfile
import subprocess
import time
from time import sleep
import signal
import requests
from multiprocessing import Pool
import os
from logger import log_error , log_success

NUM_PROCESSES = 4

CHUNK_SIZE = 200

class DownloadTimeoutError(Exception):
    pass

def timeout(wait_for):
    def set_alarm(nsecs):
        signal.signal(signal.SIGALRM, handler)
        signal.alarm(nsecs)

    def unset_alarm():
        signal.alarm(0)

    def handler(signum, frame):
        unset_alarm()
        raise DownloadTimeoutError("Download request timed out")

    def timeout_decorator(func):
        def wrapper(*args, **kwargs):
            set_alarm(wait_for)
            retval = func(*args, **kwargs)
            unset_alarm()
            return retval
        return wrapper
    return timeout_decorator

@timeout(wait_for=30)
def download_and_save_file(url: str, fname: str, session: requests.Session, retries=1):
    for attempt in range(retries):
        try:
            log_success(f'Process {os.getpid()}: Attempt {attempt + 1} for {url}')
            response = session.get(url, allow_redirects=True)
            log_success(f'Process {os.getpid()}: Status code: {response.status_code}')
            if response.status_code == 200 and len(response.content) > 0:
                log_success(f'Process {os.getpid()}: Saving to {fname}')
                with open(fname, 'wb') as fh:
                    fh.write(response.content)
            return response.status_code
        except Exception as ex:
            log_error(f'Process {os.getpid()}: Attempt {attempt + 1} failed: {ex}')
            if attempt == retries - 1:
                return None
    return None

def md5(fname):
    hash_md5 = hashlib.md5()
    try:
        with open(fname, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except:
        log_error(f"failed md5 hash {fname}")
        return None

def process_non_html_record(record, config, processed_urls):
    digital_channel, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url = record
    
    if request_url in processed_urls:
        log_error(f'Process {os.getpid()}: Skipping duplicate URL {request_url} for ID {id}')
        return
    
    processed_urls.add(request_url)
    
    documentroot = config['documentroot']
    creativesfolder = config['creativesfolder']
    siteurl = config['siteurl']
    db_config = config['db_config']
    
    fname = str(uuid.uuid4())
    file_path = f"{documentroot}/{creativesfolder}{fname}"
    
    session = requests.Session()
    session.headers.update({'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.47 Safari/537.36'})
    
    try:
        status_code = download_and_save_file(url=request_url, fname=file_path, session=session, retries=1)
        
        cnx_proc = mysql.connector.connect(**db_config)
        cursor_proc = cnx_proc.cursor()
        
        if status_code == 200:
            amd5 = md5(file_path)
            if amd5:
                query = f"UPDATE panel.tmpimportdigital SET creative_path = %s, ad_md5 = %s , download_status = %s WHERE id = %s;"
                cursor_proc.execute(query, (f"{siteurl}{creativesfolder}{fname}", amd5, 200, id))
                cnx_proc.commit()
                
                query = f"SELECT CAST(payload AS CHAR(255)) FROM panel.browserdata WHERE event_type = 'URL' AND user_ip = %s AND id < %s AND payload NOT LIKE '%chrome%' ORDER BY id DESC LIMIT 1;"
                cursor_proc.execute(query, (user_ip.replace('::', ''), id))
                row = cursor_proc.fetchone()
                if row:
                    query = f"UPDATE panel.tmpimportdigital SET site_url = %s WHERE id = %s;"
                    cursor_proc.execute(query, (row[0], id))
                    cnx_proc.commit()
                
                try:
                    media_info = MediaInfo.parse(file_path)
                    for track in media_info.tracks:
                        if track.track_type == "General" and track.format:
                            file_type = track.format
                            query = f"UPDATE panel.tmpimportdigital SET file_type = %s WHERE id = %s;"
                            cursor_proc.execute(query, (file_type, id))
                            cnx_proc.commit()
                except Exception as e:
                    log_error(f'Process {os.getpid()}: MediaInfo error for {file_path}: {e}')
        else:
            if status_code:
                query = f"UPDATE panel.tmpimportdigital SET download_status = %s WHERE id = %s;"
                cursor_proc.execute(query, (status_code, id))
                cnx_proc.commit()
                
        cursor_proc.close()
        cnx_proc.close()
    except Exception as e:
        log_error(f'Process {os.getpid()}: Error processing ID {id}: {e}')
    
    session.close()

def process_html_record(record, config, processed_urls):
    digital_channel, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url = record
    
    if request_url in processed_urls:
        log_error(f'Process {os.getpid()}: Skipping duplicate URL {request_url} for ID {id}')
        return
    
    processed_urls.add(request_url)
    
    documentroot = config['documentroot']
    creativesfolder = config['creativesfolder']
    siteurl = config['siteurl']
    srvfolder = config['srvfolder']
    db_config = config['db_config']
    
    try:
        fname = str(uuid.uuid4())
        html_path = f"{documentroot}/{creativesfolder}{fname}.html"
        copyfile(f"{srvfolder}/HTML5template.html", html_path)
        
        cH = 400
        cW = 400
        if "300x600" in request_url:
            cW, cH = 300, 600
        elif "728x90" in request_url:
            cW, cH = 728, 90
        elif "300x250" in request_url:
            cW, cH = 300, 250
        elif "320x50" in request_url:
            cW, cH = 320, 50
        elif "300x50" in request_url:
            cW, cH = 300, 50
        elif "160x600" in request_url:
            cW, cH = 160, 600
        elif "970x250" in request_url:
            cW, cH = 970, 250
        elif "970x90" in request_url:
            cW, cH = 970, 90
        
        for cmd in [
            f"sed -i 's|SBKCWIDTH|{cW}|g' {html_path}",
            f"sed -i 's|SBKCHEIGHT|{cH}|g' {html_path}",
            f"sed -i 's|SBKCSRC|{request_url}|g' {html_path}"
        ]:
            subprocess.run(cmd, shell=True, check=True)
            sleep(0.1)
        
        amd5 = md5(html_path)
        if amd5:
            cnx_proc = mysql.connector.connect(**db_config)
            cursor_proc = cnx_proc.cursor()
            
            query = f"UPDATE panel.tmpimportdigital SET creative_path = %s, ad_md5 = %s WHERE id = %s;"
            cursor_proc.execute(query, (f"{siteurl}{creativesfolder}{fname}.html", amd5, id))
            cnx_proc.commit()
            
            query = f"SELECT CAST(payload AS CHAR(255)) FROM panel.browserdata WHERE event_type = 'URL' AND user_ip = %s AND id < %s AND payload NOT LIKE '%chrome%' ORDER BY id DESC LIMIT 1;"
            cursor_proc.execute(query, (user_ip, id))
            row = cursor_proc.fetchone()
            if row:
                query = f"UPDATE panel.tmpimportdigital SET site_url = %s WHERE id = %s;"
                cursor_proc.execute(query, (row[0], id))
                cnx_proc.commit()
            
            if cW > 0:
                query = f"UPDATE panel.tmpimportdigital SET file_type = 'HTML' WHERE id = %s;"
                cursor_proc.execute(query, (id,))
                cnx_proc.commit()
            
            cursor_proc.close()
            cnx_proc.close()
    except Exception as e:
        log_error(f'Process {os.getpid()}: Error processing HTML ID {id}: {e}')

def process_records_in_chunks(cursor, config, process_func, chunk_size, processed_urls):
    while True:
        chunk = cursor.fetchmany(chunk_size)
        if not chunk:
            break
        log_success(f"Processing chunk of {len(chunk)} records with {NUM_PROCESSES} processes")
        with Pool(processes=NUM_PROCESSES) as pool:
            pool.starmap(process_func, [(record, config, processed_urls) for record in chunk])

username = 'panel'
pwd = 'U6aR8LxX5fM2vM9l^'
dbname = 'panel'
creativesfolder = 'creatives/'
documentroot = os.path.join(os.getcwd(), "var", "www" , "html")
siteurl = 'https://files-collect-sbkcenter-com.s3.us-west-2.amazonaws.com/uat/'
srvfolder = os.path.join(os.getcwd(), "srv", "parse" )
tmpfolder = os.path.join(os.getcwd(), "tmp")
hostname = 'collectdb.uat.sbkcenter.com'


if not os.path.exists(documentroot):
    os.makedirs(documentroot)   
if not os.path.exists(os.path.join(documentroot , creativesfolder)):
    os.makedirs(os.path.join(documentroot , creativesfolder))
if not os.path.exists(srvfolder):
    os.makedirs(srvfolder)
if not os.path.exists(tmpfolder):
    os.makedirs(tmpfolder)

db_config = {
    'user': username,
    'password': pwd,
    'database': dbname,
    'host': hostname
}

config = {
    'documentroot': documentroot,
    'creativesfolder': creativesfolder,
    'siteurl': siteurl,
    'srvfolder': srvfolder,
    'db_config': db_config
}

processed_urls = set()
try:
    cnx = mysql.connector.connect(**db_config)
    cursor = cnx.cursor(buffered=True)

    cnx2 = mysql.connector.connect(**db_config)
    cursor2 = cnx2.cursor()

    cnx3 = mysql.connector.connect(**db_config)
    cursor3 = cnx3.cursor()

except Exception as e:
    log_error(f"DB connection failed error: {str(e)}")
    pass

# Existing queries (unchanged)
# query = ("delete from panel.browserdata where state >0;")
# cursor.execute(query)
# cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 1');")
cursor.execute(query)
cnx.commit()
print('step1', query)

query = ("update panel.browserdata set state =1 where state =0;")
cursor.execute(query)
cnx.commit()

try:
    query = ("INSERT INTO panel.browserdata_sem SELECT * FROM browserdata WHERE event_type = 'SEARCH' AND sem_search_key != '';")
    cursor.execute(query)
    cnx.commit()
except Exception as e:
    log_error(f"failed to move data {str(e)}")
    pass

query = ("delete FROM panel.browserdata WHERE event_type = 'SEARCH' AND sem_search_key = 'null';")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.panelist_ip(ip, pemail) select distinct(ip) as ip, '' as pemail from panel.android_app_data where pemail like '%@%' and ip not in (select ip from panel.panelist_ip) and ip <> '*************';")
cursor.execute(query)
cnx.commit()

query = ("update panel.panelist_ip a inner join panel.android_app_data b on a.ip=b.ip set a.pemail = b.pemail where a.pemail = '';")
cursor.execute(query)
cnx.commit()

query = ("update panel.browserdata a inner join panel.panelist_ip b on a.user_ip=b.ip set a.pemail = b.pemail where a.event_type = 'MOBILE' and a.pemail is null;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 2');")
cursor.execute(query)
cnx.commit()
print('step2', query)

# query = ("DELETE FROM panel.tmpimportdigital WHERE status=3;")
# cursor.execute(query)
# cnx.commit()

query = ("INSERT INTO panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url) SELECT 'Online Display' as digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url FROM panel.browserdata WHERE request_url LIKE '%tpc.goog%simgad%' AND request_url NOT LIKE '%daca%' AND request_url NOT LIKE '%html%' AND event_type = 'RESOURCE' AND state =1 AND pemail LIKE '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("INSERT INTO panel.tmpimportdigital( digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url ) SELECT 'Online Display' AS digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url FROM panel.browserdata WHERE request_url LIKE '%2mdn%' AND payload NOT LIKE '%2mdn%' AND request_url NOT LIKE '%html%' AND request_url NOT LIKE '%.js' AND request_url NOT LIKE '%.mp4' AND request_url NOT LIKE '%1x1%' AND request_url NOT LIKE '%dot.gif%' AND request_url NOT LIKE '%richmedia%' AND request_url NOT LIKE '%videoplayback%' AND request_url NOT LIKE '%.webm' AND request_url NOT LIKE '%.swf%' AND event_type = 'RESOURCE' AND state = 1 AND pemail LIKE '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%cdn.adlegend.com%.html%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url ) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%ad.atdmt.com/as/direct%.html%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url ) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%cdn.flashtalking.com%.html%' and request_url not like '%index.html%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like 'https://tpc.googlesyndication.com/%.html%' and request_url not like '%index.html%' and request_url not like '%gadget%' and request_url not like '%container%' and request_url not like '%sodar%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , replace(request_url,'?e=69','') as request_url from panel.browserdata where request_url like '%s0.2mdn.net/%.html%' and payload not like '%2mdn%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where (request_url like '%300x600%.html%' or request_url like '%728x90%.html%' or request_url like '%300x250%.html%' or request_url like '%320x50%.html%' or request_url like '%300x50%.html%' or request_url like '%160x600%.html%' or request_url like '%970x250%.html%' or request_url like '%970x90%.html%' ) and request_url not like '%cdn.adlegend.com%' and request_url not like '%atdmt%' and request_url not like '%flashtalking%' and request_url not like '%googlesynd%' and request_url not like '%2mdn.net%' and request_url not like '%doubleclick%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Mobile' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%2mdn%' and payload not like '%2mdn%' and request_url not like '%html%' and request_url not like '%.js' and request_url not like '%.mp4' and request_url not like '%1x1%' and request_url not like '%dot.gif%' and request_url not like '%richmedia%' and request_url not like '%videoplayback%' and request_url not like '%.webm' and request_url not like '%.swf%' and event_type = 'MOBILE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = (" insert into panel.tmpimportdigital (digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%tpc.goog%simgad%' and request_url not like '%daca%'  and request_url not like '%html%'  and event_type = 'MOBILE' and state =1 and pemail like '%@%';")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url )   select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%cdn.adlegend.com%.html%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%ad.atdmt.com/as/direct%.html%' and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)   select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%cdn.flashtalking.com%.html%' and request_url not like '%index.html%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like 'https://tpc.googlesyndication.com/%.html%' and request_url not like '%index.html%' and  request_url not like '%gadget%' and  request_url not like '%container%'  and  request_url not like '%sodar%'    and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , replace(request_url,'?e=69','') as request_url   from panel.browserdata where request_url like '%s0.2mdn.net/%.html%' and payload not like '%2mdn%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where (request_url like '%300x600%.html%' or request_url like '%728x90%.html%' or request_url like '%300x250%.html%' or request_url like '%320x50%.html%' or request_url like '%300x50%.html%' or request_url like '%160x600%.html%' or request_url like '%970x250%.html%' or request_url like '%970x90%.html%' ) and request_url not like '%cdn.adlegend.com%'  and request_url not like '%atdmt%'   and request_url not like '%flashtalking%'   and request_url not like '%googlesynd%'   and request_url not like '%2mdn.net%' and request_url not like '%doubleclick%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%';")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url) select 'Online Video' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where (request_url like '%innovid.com%mp4' or request_url like '%ads.eyeviewads.com%mp4' or request_url like '%tribalfusion.com%mp4' or request_url like '%2mdn.net/videoplayback/%file.mp4') and event_type = 'RESOURCE'  and state =1  and pemail like '%@%' ; ")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url) select 'Mobile Video' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where (request_url like '%innovid.com%mp4' or request_url like '%ads.eyeviewads.com%mp4' or request_url like '%tribalfusion.com%mp4' or request_url like '%2mdn.net/videoplayback/%file.mp4') and event_type = 'MOBILE'  and state =1  and pemail like '%@%' ; ")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 3');")
cursor.execute(query)
cnx.commit()

try:
    query = ("select digital_channel, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url from panel.tmpimportdigital where request_url not like '%html%' order by id;")
    cursor.execute(query)
    start_time = time.time()
    process_records_in_chunks(cursor, config, process_non_html_record, chunk_size=CHUNK_SIZE, processed_urls=processed_urls)
    cursor.execute("INSERT INTO panel.log_cron(cron_step) VALUE (%s);", (f"Non-HTML processing took {time.time() - start_time:.2f} seconds",))
    cnx.commit()
except Exception as e :
    log_error(f"failed to process non html data {str(e)}")
    pass

try:
    query = ("select digital_channel, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url from panel.tmpimportdigital where request_url like '%html%';")
    cursor.execute(query)
    start_time = time.time()
    process_records_in_chunks(cursor, config, process_html_record, chunk_size=CHUNK_SIZE, processed_urls=processed_urls)
    cursor.execute("INSERT INTO panel.log_cron(cron_step) VALUE (%s);", (f"HTML processing took {time.time() - start_time:.2f} seconds",))
    cnx.commit()
except Exception as e:
    log_error(f"failed to process html data {str(e)}")
    pass                                                                                  


query = ("insert into panel.log_cron(cron_step) value ('step 4');")
cursor.execute(query)
cnx.commit()
print('step4', query)

query = ("delete from panel.tmpimportdigital where digital_channel = 'Online Display' and (file_type is null OR file_type = ''); ")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 5');")
cursor.execute(query)
cnx.commit()
print('step 5', query)

query = (" update panel.tmpimportdigital set site_domain = LEFT(SUBSTRING_INDEX((SUBSTRING_INDEX((SUBSTRING_INDEX(replace(site_url, 'https://', 'http://'), 'http://', -1)), '/', 1)), '.', -2),100) ; ")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigitalnewcreative(ad_md5) select distinct(ad_md5) as ad_md5 from panel.tmpimportdigital where ad_md5 not in (select ad_md5 from panel.tmpuniqueads);")
cursor.execute(query)
cnx.commit()

query = ("update panel.tmpimportdigitalnewcreative a inner join panel.tmpimportdigital b on a.ad_md5=b.ad_md5 set a.creative_path=b.creative_path, a.digital_channel=b.digital_channel, a.file_type=b.file_type ;")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpimportdigitalnewcreative where creative_path is null;")
cursor.execute(query)
cnx.commit()

query = ("update panel.browserdata set state =2 where state =1;")
cursor.execute(query)
cnx.commit()

query = ("drop table if exists panel.tmpurlimport;")
cursor.execute(query)
cnx.commit()

query = ("create table panel.tmpurlimport as select * from panel.browserdata where event_type = 'URL' and state =2; ")
cursor.execute(query)
cnx.commit()

query = ("ALTER TABLE tmpurlimport ADD PRIMARY KEY(id),ADD INDEX user_ip (user_ip)")
cursor.execute(query)
cnx.commit()

query = ("alter table panel.tmpurlimport add column simple_domain varchar(100); ")
cursor.execute(query)
cnx.commit()

query = ("update panel.tmpurlimport set simple_domain = left(SUBSTRING_INDEX((SUBSTRING_INDEX((SUBSTRING_INDEX(replace(payload, 'https://', 'http://'), 'http://', -1)), '/', 1)), '.', -2) ,100); ")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpurlimport where simple_domain ='' or simple_domain is null; ")
cursor.execute(query)
cnx.commit()

query = ("drop table if exists panel.tmpurlsummary; ")
cursor.execute(query)
cnx.commit()

query = ("create table panel.tmpurlsummary as select pemail, payload, user_ip, min(id) as id from panel.tmpurlimport group by pemail, payload, user_ip; ")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpurlsummary where payload like '%chrome://%'; ")
cursor.execute(query)
cnx.commit()

query = ("alter table panel.tmpurlsummary add column site_url varchar(300), add column simple_domain varchar(100) ; ")
cursor.execute(query)
cnx.commit()

query = ("update panel.tmpurlsummary set site_url = left(payload,300); ")
cursor.execute(query)
cnx.commit()

query = ("update panel.tmpurlsummary set simple_domain = left(SUBSTRING_INDEX((SUBSTRING_INDEX((SUBSTRING_INDEX(replace(payload, 'https://', 'http://'), 'http://', -1)), '/', 1)), '.', -2) ,100); ")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpurlimport where id not in (select id from panel.tmpurlsummary); ")
cursor.execute(query)
cnx.commit()

# query = ("delete from panel.browserdata where state =2;")
# cursor.execute(query)
# cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 6');")
cursor.execute(query)
cnx.commit()
print('step 6', query)

query = ("insert into panel.cron_sequence(execution) value ('1');")
cursor.execute(query)
cnx.commit()

cnx.close()
cnx2.close()
cnx3.close()