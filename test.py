# pip install selenium-wire
# pip install beautifulsoup4

from seleniumwire import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import time
import os

# Scrape.do token (replace with yours)
SCRAPEDO_TOKEN = "b31f8211d2d54c188b00a93623d90afa21be4fb57e7"

# Use `render=true` to enable JS rendering via Scrape.do's proxy
proxy_password = "render=true"

# Proxy formatted for Scrape.do
proxy_url = f"http://{SCRAPEDO_TOKEN}:{proxy_password}@proxy.scrape.do:8080"

# Selenium Wire options
seleniumwire_options = {
    "proxy": {
        "http": proxy_url,
        "https": proxy_url,
        "no_proxy": "localhost,127.0.0.1",
        "verify_ssl": False
    }
}

# Chrome options
chrome_options = Options()
chrome_options.add_argument('--disable-blink-features=AutomationControlled')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')
chrome_options.add_argument('--headless=new')  # Run headless (you can remove this to see the browser)

# Initialize driver
driver = webdriver.Chrome(
    options=chrome_options,
    seleniumwire_options=seleniumwire_options
)

try:
    # Your target URL (e.g. Google search, or any JS-heavy site)
    search_query = "best ai ml courses"
    search_url = f"https://www.google.com/search?q={search_query}"
    
    print(f"Fetching: {search_url}")
    driver.get(search_url)

    # Wait for JS to load — increase sleep for heavy pages
    time.sleep(5)

    # Get fully rendered HTML
    rendered_html = driver.page_source

    # Optional: Use BeautifulSoup to prettify
    soup = BeautifulSoup(rendered_html, "html.parser")

    # Save to file
    output_file = "rendered_page.html"
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(soup.prettify())

    print(f"Saved rendered page to {output_file}")

finally:
    driver.quit()
