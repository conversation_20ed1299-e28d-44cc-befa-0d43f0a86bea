import mysql.connector
import uuid
import hashlib
from shutil import copyfile
import subprocess
import time
from time import sleep
from bs4 import BeautifulSoup as Soup
import random
import undetected_chromedriver as uc
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from urllib.parse import quote
import logging
import os
import traceback
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import MoveTargetOutOfBoundsException
from selenium.webdriver.common.keys import Keys
from seleniumwire import webdriver 
from selenium.webdriver.chrome.options import Options 
import requests

class AppLogger:
    def __init__(self):
        logger = logging.getLogger("AppLogger")
        logger.setLevel(logging.DEBUG)

        if not logger.handlers:
            log_dir = os.path.join(os.getcwd(), "logs")
            os.makedirs(log_dir, exist_ok=True)
            file_name = os.path.join(log_dir, "sem_process.log")

            handler = logging.FileHandler(file_name)
            formatter = logging.Formatter(
                '%(asctime)s %(levelname)s:%(name)s [%(filename)s:%(lineno)d] %(message)s'
            )
            handler.setFormatter(formatter)
            handler.setLevel(logging.DEBUG)
            logger.addHandler(handler)

        self._logger = logger

    def get(self):
        return self._logger

logger = AppLogger().get()

def md5(fname):
    hash_md5 = hashlib.md5()
    with open(fname, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def addslashes(s):
    if s is not None:
        l = ["\\", '"', "'", "\0"]
        for i in l:
            if i in s:
                s = s.replace(i, '\\'+i)
    return s

def add_random_delay(min_delay=2, max_delay=5):
    """Add random delay between actions"""
    time.sleep(random.uniform(min_delay, max_delay))

def detect_captcha(driver):
    """Check if CAPTCHA is present on the page"""
    try:
        captcha = driver.find_elements(By.ID, "recaptcha") or \
                 driver.find_elements(By.CLASS_NAME, "g-recaptcha") or \
                 driver.find_elements(By.XPATH, "//*[contains(text(), 'prove you are not a robot')]")
        return bool(captcha)
    except Exception as e:
        logger.error(f"Error detecting CAPTCHA: {e}")
        return False

def handle_captcha(driver):
    """Attempt to bypass CAPTCHA (basic approach, may need external service for complex CAPTCHAs)"""
    try:
        logger.info("CAPTCHA detected, attempting to handle")
        # Placeholder for CAPTCHA solving (e.g., using 2Captcha or Anti-Captcha service)
        # For now, just wait and try to proceed
        add_random_delay(5, 10)
        driver.refresh()
        return True
    except Exception as e:
        logger.error(f"Failed to handle CAPTCHA: {e}")
        return False

def wait_for_search_results(driver, timeout=20):
    try:
        logger.debug("Waiting for search results to load")
        try:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.ID, "search"))
            )
            logger.debug("Search results loaded via ID 'search'")
        except:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.CLASS_NAME, "g"))
            )
            logger.debug("Search results loaded via class 'g'")
        
        if detect_captcha(driver):
            logger.warning("CAPTCHA detected")
            if not handle_captcha(driver):
                return False
        return True
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1]
        error_message = f"Error waiting for search results: {e} [Line: {tb.lineno}]"
        logger.error(error_message)
        return False

def natural_scroll(driver):
    try:
        """Implementing natural scrolling behavior"""
        total_height = driver.execute_script("return document.body.scrollHeight")
        current_position = 0
        
        while current_position < total_height:
            scroll_step = random.randint(100, 400)
            current_position += scroll_step
            driver.execute_script(f"window.scrollTo(0, {current_position});")
            add_random_delay(0.2, 0.5)
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1]
        error_message = f"Scroll error: {e} [Line: {tb.lineno}]"
        logger.error(error_message)

def simulate_human_interaction(driver, element, logger):
    try:
        # Log element position and size for debugging
        rect = element.rect
        logger.debug(f"Element position: x={rect['x']}, y={rect['y']}, "
                     f"width={rect['width']}, height={rect['height']}")

        # Scroll element into view before interaction
        driver.execute_script("arguments[0].scrollIntoView({behavior: 'auto', block: 'center'});", element)

        # Optional wait for smooth scroll (e.g., 0.3s)
        import time
        time.sleep(0.3)

        # Move to element and perform action
        ActionChains(driver).move_to_element(element).perform()
        logger.info("Simulated human interaction successfully.")

    except MoveTargetOutOfBoundsException as e:
        logger.error(f"MoveTargetOutOfBoundsException: {e}")

    except Exception as e:
        logger.error(f"Error simulating human interaction: {e}")

def get_proxy():
    """Fetch a proxy from ScraperAPI (or another proxy service)"""
    try:
        proxy_key = '0984b24654ed709184f31e154c4aca5a'
        proxy_url = f"http://proxy.scraperapi.com:8001?api_key={proxy_key}"
        return proxy_url
    except Exception as e:
        logger.error(f"Error fetching proxy: {e}")
        return None


def create_driver(path=None, max_retries=3):
    for attempt in range(max_retries):
        try:
            chrome_options = Options()

            # 📌 Core settings
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')
            chrome_options.add_argument('--disable-infobars')
            chrome_options.add_argument('--disable-notifications')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--headless=chrome')  # Changed from --headless=new
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--start-maximized')
            chrome_options.add_argument('--lang=en-US')
            chrome_options.add_argument('--accept-lang=en-US,en;q=0.9')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-site-isolation-trials')

            # Block images (optional)
            chrome_options.add_experimental_option("prefs", {
                "profile.default_content_setting_values": {
                    "images": 2
                }
            })

            # ✅ Encoded authenticated proxy setup
            raw_username = "b31f8211d2d54c188b00a93623d90afa21be4fb57e7"
            raw_password = "render=false"
            encoded_username = quote(raw_username)
            encoded_password = quote(raw_password)
            proxy = f"http://{encoded_username}:{encoded_password}@proxy.scrape.do:8080"

            seleniumwire_options = {
                "proxy": {
                    "http": proxy,
                    "https": proxy,
                    "verify_ssl": False
                }
            }

            # driver = webdriver.Chrome(
            #     options=chrome_options,
            #     seleniumwire_options=seleniumwire_options
            # )
            driver = webdriver.Chrome(options=chrome_options)

            # 🧬 Stealth tweak
            driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
            )

            logger.info("Driver created (JS enabled, images blocked)")
            return driver

        except Exception as e:
            tb = traceback.extract_tb(e.__traceback__)[-1]
            logger.error(f"Driver creation attempt {attempt+1} failed: {e} [Line: {tb.lineno}]")
            if attempt < max_retries - 1:
                add_random_delay(5, 10)
            else:
                return None

def save_page(driver, id=1):
    try:
        """Save the page source to an HTML file"""
        page_source = driver.page_source
        try:
            with open("abc.html", 'w', encoding='utf-8') as f:
                f.write(page_source)
            logger.info("Saved page for diagnosis")
        except Exception as e:
            logger.error(f"Error saving content to abc.html: {str(e)}")
        add_random_delay(3, 6)
        copyfile("abc.html", f"sem_html/{id}.html")
        logger.info("Saved HTML file successfully")
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1]
        error_message = f"Save page error: {e} [Line: {tb.lineno}]"
        logger.error(error_message)
    finally:
        logger.info("Driver closed")
        driver.quit()

username = 'panel'
pwd = 'U6aR8LxX5fM2vM9l^'
dbname = 'panel'
creativesfolder = 'creatives/'
documentroot = os.path.join(os.getcwd(), "var", "www", "html/")
siteurl = 'https://files-collect-sbkcenter-com.s3.us-west-2.amazonaws.com/uat/'
srvfolder = os.path.join(os.getcwd(), "srv", "parse/")
tmpfolder = os.path.join(os.getcwd(), "tmp/")
hostname = 'collectdb.uat.sbkcenter.com'
driver_path = os.path.join(os.getcwd(), "drivers", "undetected_chromedriver")

if not os.path.exists(documentroot):
    os.makedirs(documentroot)
if not os.path.exists(os.path.join(documentroot, creativesfolder)):
    os.makedirs(os.path.join(documentroot, creativesfolder))
if not os.path.exists(srvfolder):
    os.makedirs(srvfolder)
if not os.path.exists(tmpfolder):
    os.makedirs(tmpfolder)

db_config = {
    'user': username,
    'password': pwd,
    'database': dbname,
    'host': hostname
}

user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 14.2; rv:109.0) Gecko/20100101 Firefox/115.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Edge/********* Safari/537.36'
]

try:
    cnx = mysql.connector.connect(**db_config)
    cursor = cnx.cursor(buffered=True)

    cnx2 = mysql.connector.connect(**db_config)
    cursor2 = cnx2.cursor()

    cnx3 = mysql.connector.connect(**db_config)
    cursor3 = cnx3.cursor()

    query = ("select 'Search Engine Marketing' as digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, replace(payload,'\n','NEWLINE') as payload, tab_id, request_url, sem_search_key from panel.browserdata_sem where event_type = 'SEARCH' and payload != '' and state = 1 and pemail like '%@%';")
    cursor.execute(query)
    for (digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url, sem_search_key) in cursor:
        logger.info(f"Search key: {sem_search_key}, ID: {id}")
        if sem_search_key is not None:
            max_attempts = 3
            for attempt in range(max_attempts):
                driver = create_driver(driver_path)
                if not driver:
                    logger.error("Failed to create driver")
                    break
                
                try:
                    encoded_term = quote(sem_search_key)
                    search_url = f"https://www.google.com/search?q={encoded_term}"
                    driver.get(search_url)
                    
                    add_random_delay(1.5, 4.5)
                    element = driver.find_element(By.ID, "search")
                    simulate_human_interaction(driver, element, logger)


                    
                    if wait_for_search_results(driver):
                        natural_scroll(driver)
                        save_page(driver, id)
                        
                        with open("abc.html", "r", encoding="utf-8") as file:
                            html_text = Soup(file, "html.parser")

                        method = 1
                        add_blocks = html_text.find_all('li', {'class': 'ads-ad'})

                        if not add_blocks:
                            add_blocks = html_text.find_all('li', {'class': 'ads-fr'})
                            method = 2
                        if not add_blocks:
                            add_blocks = html_text.find_all('div', {'class': 'uEierd'})
                            method = 3
                        
                        if len(add_blocks) > 0:
                            for add_block in add_blocks:
                                line1 = line2 = line3 = ''
                                if method == 1:
                                    add_blk = add_block.find('div', {'class': 'ads-visurl'})
                                    if add_blk is not None:
                                        line1add = add_blk.find('span').get_text()
                                        logger.info(line1add)
                                        add_blkcite = add_block.find('cite')
                                        line1 = add_blkcite.get_text()
                                        logger.info(line1)

                                        line2_blk = add_block.find('h3')
                                        line2 = str.format(line2_blk.get_text()).strip()
                                        logger.info(line2)

                                        line3_blk = add_block.find('div', {'class': 'ads-creative'})
                                        line3 = line3_blk.get_text()
                                        logger.info(line3)
                                elif method == 2:
                                    add_blk = add_block.find('span', {'class': 'gBIQub'})
                                    if add_blk is not None:
                                        line1add = add_blk.find('span').get_text()
                                        line1 = line1add
                                        logger.info(line1)
                                        line1add2 = add_block.find('div', {'role': 'heading'}).get_text()
                                        line2 = str.format(line1add2).strip()
                                        logger.info(line1add2)
                                        line3_blk = add_block.find_all('div', {'class': 'MUxGbd'})[1]
                                        line3 = line3_blk.find('span').get_text()
                                elif method == 3:
                                    line1 = line2 = line3 = ''
                                    line1add1 = add_block.find('span', {'role': 'text'})
                                    if line1add1 is not None:
                                        line1add1 = line1add1.get_text()
                                        line1 = str.format(line1add1).strip()
                                        logger.info(line1)

                                    line1add2 = add_block.find('div', {'role': 'heading'})
                                    if line1add2 is not None:
                                        line1add2 = line1add2.get_text()
                                        line2 = str.format(line1add2).strip()
                                        logger.info(line2)
                                    line3_blk = add_block.find_all('div', {'class': 'v5yQqb'})
                                    if isinstance(line3_blk, list) and len(line3_blk) > 0:
                                        line3_blk = line3_blk[0]
                                        line3 = str.format(line3_blk.get_text()).strip()
                                        logger.info(line3)

                                line1 = line1.replace("SBKCCHAR1", "`").replace("&", "and").replace("'", "").replace("`", "").replace('"', '').replace("|", "")
                                pcall = "echo 'line1 = "  + line1.replace("'","SQ") + "' >> "+str(tmpfolder)+"semlogging.txt "
                                p = subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
                                
                                line2 = line2.replace("Ads", "www").replace("SBKCCHAR1", "`").replace("&", "and").replace("'", "").replace("`", "").replace('"', '').replace("|", "")
                                pcall = "echo 'line2 = "  + line2.replace("'","SQ") + "' >> "+str(tmpfolder)+"semlogging.txt "
                                p = subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)

                                t = str(line3).replace(".", "").replace(" ", "")
                                if t.isdigit():
                                    line3 = line3.replace("SBKCCHAR1", "`").replace("&", "and").replace("'", "").replace("`", "").replace('"', '').replace("|", "")
                                else:
                                    line3 = line3.replace("SBKCCHAR1", "`").replace("&", "and").replace("'", "").replace("`", "").replace('"', '').replace("|", "")
                                line3 = line3.replace(" My Ad Centre", "")
                                pcall = "echo 'line3 = "  + line3.replace("'","SQ") + "' >> "+str(tmpfolder)+"semlogging.txt "
                                p = subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)

                                copyfile(f"{srvfolder}searchtemplate.html", f"{srvfolder}working.html")
                                pcall = "sed -i 's|SEMRESULTSLINE1|" + line1.replace("'","").replace("`","").replace('"','') + "|g' "+str(srvfolder)+"working.html"
                                p = subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
                                p.communicate()
                                sleep(1)
                                pcall = "sed -i 's|SEMRESULTSLINE2|" + line2.replace("'","").replace("`","").replace('"','') + "|g' "+str(srvfolder)+"working.html"
                                p = subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
                                p.communicate()
                                sleep(1)
                                pcall = "sed -i 's|SEMRESULTSLINE3|" + line3.replace("'","").replace("`","").replace('"','') + "|g' "+str(srvfolder)+"working.html"
                                p = subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
                                p.communicate()
                                sleep(1)
                                fname = uuid.uuid4()
                                amd5 = md5(f"{srvfolder}working.html")
                                try:
                                    pcall = f"xvfb-run -a wkhtmltoimage {srvfolder}working.html {documentroot}{creativesfolder}{fname}.png"
                                    p = subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
                                    p.communicate()
                                except Exception as e:
                                    logger.error(f"Failed to save photo error: {str(e)}")
                                query = ("insert into panel.semdetails(sem_id, ad_md5, sem_headline, sem_url, sem_description, sem_search_key) values (%s, %s, %s, %s, %s, %s);")
                                cursor2.execute(query, (str(id), str(amd5), str(addslashes(line2)), str(addslashes(line1)), str(addslashes(line3)), str(addslashes(sem_search_key))))
                                cnx2.commit()

                                query = (f"insert into panel.tmpimportdigital(digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url, creative_path, ad_md5, site_url, site_domain, panelist_id, file_type) values ('Search Engine Marketing', '{pemail}', {id}, '{ts}', 0, 'SEARCH', '', '{user_ip}', '{line2}', '', 'google.com/search', '{siteurl}{creativesfolder}{fname}.png', '{amd5}', 'https://www.google.com/search', 'google.com', 0, 'PNG');")
                                cursor2.execute(query)
                                cnx2.commit()
                        break  # Success, exit retry loop
                    else:
                        logger.error(f"Attempt {attempt + 1} failed for search key: {sem_search_key}")
                        if attempt < max_attempts - 1:
                            add_random_delay(10, 20)
                            driver.quit()
                            continue
                        logger.error(f"Max attempts reached for search key: {sem_search_key}")
                except Exception as e:
                    tb = traceback.extract_tb(e.__traceback__)[-1]
                    error_message = f"Processing error on attempt {attempt + 1}: {e} [Line: {tb.lineno}]"
                    logger.error(error_message)
                    if attempt < max_attempts - 1:
                        add_random_delay(10, 20)
                        driver.quit()
                        continue
                    logger.error(f"Max attempts reached for search key: {sem_search_key}")
                finally:
                    if driver:
                        driver.quit()

    query = ("insert into panel.log_cron(cron_step) value ('step 5');")
    cursor.execute(query)
    cnx.commit()
    logger.info('step 5 : Done')

    query = ("update panel.browserdata_sem set state = 2 where state = 1;")
    cursor.execute(query)
    cnx.commit()

    query = ("insert into panel.log_cron(cron_step) value ('step 7');")
    cursor.execute(query)
    cnx.commit()
    logger.info('step 7 : Done')

except Exception as e:
    tb = traceback.extract_tb(e.__traceback__)[-1]
    error_message = f"Main loop error: {e} [Line: {tb.lineno}]"
    logger.error(error_message)

finally:
    cursor.close()
    cnx.close()
    cursor2.close()
    cnx2.close()
    cursor3.close()
    cnx3.close()