import mysql.connector
import uuid
import hashlib
from shutil import copyfile
import subprocess
import time
from time import sleep
from bs4 import BeautifulSoup as Soup
import random
import undetected_chromedriver as uc
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from urllib.parse import quote
import logging
import os
import urllib.parse
import requests
import traceback
from selenium.webdriver.common.action_chains import ActionChains
from seleniumwire import webdriver
from selenium.webdriver.chrome.options import Options

class AppLogger:
    def __init__(self):
        logger = logging.getLogger("AppLogger")
        logger.setLevel(logging.DEBUG)

        if not logger.handlers:
            log_dir = os.path.join(os.getcwd(), "logs")
            os.makedirs(log_dir, exist_ok=True)
            file_name = os.path.join(log_dir, "sem_process.log")

            handler = logging.FileHandler(file_name)
            formatter = logging.Formatter(
                '%(asctime)s %(levelname)s:%(name)s [%(filename)s:%(lineno)d] %(message)s'
            )
            handler.setFormatter(formatter)
            handler.setLevel(logging.DEBUG)
            logger.addHandler(handler)

        self._logger = logger

    def get(self):
        return self._logger

logger = AppLogger().get()

def md5(fname):
    hash_md5 = hashlib.md5()
    with open(fname, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def addslashes(s):
    if s is not None:
        l = ["\\", '"', "'", "\0"]
        for i in l:
            if i in s:
                s = s.replace(i, '\\'+i)
    return s

def add_random_delay(min_delay=2, max_delay=5):
    """Add random delay between actions"""
    time.sleep(random.uniform(min_delay, max_delay))

def wait_for_search_results(driver, timeout=20):
    try:
        logger.debug("Waiting for search results to load")
        try:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.ID, "search"))
            )
            logger.info("Search results loaded with ID 'search'")
        except:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.CLASS_NAME, "g"))
            )
            logger.info("Search results loaded with class 'g'")
        logger.debug("Search results loaded successfully")
        return True
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1]
        error_message = f"Error waiting for search results: {e} [Line: {tb.lineno}]"
        logger.error(error_message)
        return False

def natural_scroll(driver):
    try:
        total_height = driver.execute_script("return document.body.scrollHeight")
        current_position = 0
        while current_position < total_height:
            scroll_step = random.randint(100, 300)
            current_position += scroll_step
            driver.execute_script(f"window.scrollTo(0, {current_position});")
            add_random_delay(0.1, 0.3)
        # search_container = driver.find_element(By.ID, "search")
        # driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth'});", search_container)
        # actions = ActionChains(driver)
        # actions.move_by_offset(random.randint(50, 200), random.randint(50, 200)).perform()
        # add_random_delay(0.1, 0.3)
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1] 
        error_message = f"error: {e} [Line: {tb.lineno}]"
        logger.error(error_message)

def create_driver(path):
    try:
        options = uc.ChromeOptions()
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument(f'--user-agent={random.choice(user_agents)}')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--headless=new')
        options.add_argument(f'--proxy-server={proxy_url}')
        try:
            driver = uc.Chrome(options=options, use_subprocess=True,version_main=133,driver_executable_path=path)
            logger.info("Working with local installed driver")
        except Exception as e:
            logger.warning(f"Failed with auto-detection, trying specific version: {e}")
            driver = uc.Chrome(options=options, use_subprocess=True, version_main=133)
            logger.error(f"Going with local path")
        logger.info(f"Driver created successfully at: {driver.service.path}")
        return driver
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1] 
        error_message = f"error: {e} [Line: {tb.lineno}]"
        logger.error(error_message)
        return None

def save_page(driver, id=1):
    try:
        """Save the page source to an HTML file"""
        page_source = driver.page_source
        try:
            with open("abc.html", 'w', encoding='utf-8') as f:
                f.write(page_source)
            logger.info("Saved page for diagnosis")
        except Exception as e:
            logger.error(f"Getting error saving contant to abc.html {str(e)}")
        add_random_delay(3, 6)
        copyfile("abc.html", f"sem_html/{id}.html")
        logger.info("Saved HTML file successfully")
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1]
        error_message = f"Error saving page: {e} [Line: {tb.lineno}]"
        logger.error(error_message)
    finally:
        logger.info("Driver closed")
        driver.quit()
        

user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:88.0)"
]

def fetch_google_results_scrapedo(sem_search_key, save_as="abc.html"):
    try:
        SCRAPEDO_TOKEN = "b31f8211d2d54c188b00a93623d90afa21be4fb57e7"
        base_url = "http://api.scrape.do/"

        # Construct the full target URL with query parameters encoded inside it
        target_url = f"https://www.google.com/search?q={urllib.parse.quote(sem_search_key)}"
        encoded_target_url = urllib.parse.quote(target_url)

        # Build final URL for Scrape.do API
        api_url = f"{base_url}?token={SCRAPEDO_TOKEN}&url={encoded_target_url}&render=true"

        headers = {
            "User-Agent": random.choice(user_agents)
        }

        logger.info(f"Fetching from Scrape.do API: {api_url}")
        response = requests.get(api_url, headers=headers, timeout=30)

        if response.status_code == 200:
            with open(save_as, "w", encoding="utf-8") as f:
                f.write(response.text)
            logger.info(f"Scrape.do: HTML saved to {save_as}")
            return True
        else:
            logger.error(f"Scrape.do failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)[-1]
        logger.error(f"Exception in fetch_google_results_scrapedo: {e} [Line: {tb.lineno}]")
        return False



username = 'panel'
pwd = 'U6aR8LxX5fM2vM9l^'
dbname = 'panel'
creativesfolder = 'creatives/'
documentroot = os.path.join(os.getcwd(), "var", "www", "html/")
siteurl = 'https://files-collect-sbkcenter-com.s3.us-west-2.amazonaws.com/uat/'
srvfolder = os.path.join(os.getcwd(), "srv", "parse/")
tmpfolder = os.path.join(os.getcwd(), "tmp/")
hostname = 'collectdb.uat.sbkcenter.com'
driver_path = os.path.join(os.getcwd(), "drivers", "undetected_chromedriver")
proxy_key = '20W31FAAF2D1AHAIH646TNYKX723Y4Q8G9WKB3FMXN39O8N2K0OBMFOWA27ZHCVUQV05GL69S0NDYO29'  # Replace with your ScrapingBee API key
proxy_url = f'http://{proxy_key}:@proxy.scrapingbee.com:8886'

if not os.path.exists(documentroot):
    os.makedirs(documentroot)
if not os.path.exists(os.path.join(documentroot, creativesfolder)):
    os.makedirs(os.path.join(documentroot, creativesfolder))
if not os.path.exists(srvfolder):
    os.makedirs(srvfolder)
if not os.path.exists(tmpfolder):
    os.makedirs(tmpfolder)

db_config = {
    'user': username,
    'password': pwd,
    'database': dbname,
    'host': hostname
}

user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
]


try:

    cnx = mysql.connector.connect(**db_config)
    cursor = cnx.cursor(buffered=True)

    cnx2 = mysql.connector.connect(**db_config)
    cursor2 = cnx2.cursor()
    
    cnx3 = mysql.connector.connect(**db_config)
    cursor3 = cnx3.cursor()


    # Delete records where state is not equal to 1
    query_delete = ("DELETE FROM panel.browserdata_sem WHERE state != 1;")
    cursor.execute(query_delete)
    cnx.commit()
    logger.info("Deleted browserdata_sem records where state != 1")

    query = ("select 'Search Engine Marketing' as digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, replace(payload,'\n','NEWLINE') as payload, tab_id, request_url, sem_search_key from panel.browserdata_sem where event_type = 'SEARCH' and payload !='' and state =1 and pemail like '%@%' ;")
    cursor.execute(query)
    for (digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url, sem_search_key) in cursor:
        logger.info(f"Search key: {sem_search_key}, ID: {id}")
        if sem_search_key is not None:
            if fetch_google_results_scrapedo(sem_search_key, save_as="abc.html"):
                with open("abc.html", "r", encoding="utf-8") as file:
                    html_text = Soup(file, "html.parser")
                logger.info("HTML fetched and parsed successfully")
            else:
                logger.warning(f"Failed to fetch for keyword: {sem_search_key}")
                continue

        
            with open("abc.html", "r", encoding="utf-8") as file:
                html_text = Soup(file, "html.parser")

            method = 1
            add_blocks = html_text.find_all('li', {'class': 'ads-ad'})

            if not add_blocks:
                add_blocks = html_text.find_all('li', {'class': 'ads-fr'})
                method = 2
            if not add_blocks:
                add_blocks = html_text.find_all('div', {'class': 'uEierd'})
                method = 3
            
            if len(add_blocks) > 0:
                for add_block in add_blocks:
                    line1 = line2 = line3 = ''
                    if method == 1:
                        add_blk = add_block.find('div', {'class': 'ads-visurl'})
                        if add_blk is not None:
                            line1add = add_blk.find('span').get_text()
                            logger.info(line1add)
                            add_blkcite = add_block.find('cite')
                            line1 = add_blkcite.get_text()
                            logger.info(line1)

                            line2_blk = add_block.find('h3')
                            line2 = str.format(line2_blk.get_text()).strip()
                            logger.info(line2)

                            line3_blk = add_block.find('div', {'class': 'ads-creative'})
                            line3 = line3_blk.get_text()
                            logger.info(line3)
                    elif method == 2:
                        add_blk = add_block.find('span', {'class': 'gBIQub'})
                        if add_blk is not None:
                            line1add = add_blk.find('span').get_text()
                            line1 = line1add
                            logger.info(line1)
                            line1add2 = add_block.find('div', {'role': 'heading'}).get_text()
                            line2 = str.format(line1add2).strip()
                            logger.info(line1add2)
                            line3_blk = add_block.find_all('div', {'class': 'MUxGbd'})[1]
                            line3 = line3_blk.find('span').get_text()
                    elif method == 3:
                        line1add1 = add_block.find('span', {'role': 'text'})
                        if line1add1 is not None:
                            line1add1 = line1add1.get_text()
                            line1 = str.format(line1add1).strip()
                            logger.info(line1)
                        line1add2 = add_block.find('div', {'role': 'heading'})
                        if line1add2 is not None:
                            line1add2 = line1add2.get_text()
                            line2 = str.format(line1add2).strip()
                            logger.info(line2)
                        line3_blk = add_block.find_all('div', {'class': 'v5yQqb'})
                        if isinstance(line3_blk, list) and len(line3_blk) > 0:
                            line3_blk = line3_blk[0]
                            line3 = str.format(line3_blk.get_text()).strip()
                            logger.info(line3)
                    
                    # Clean strings before constructing sed commands
                    cleaned_line1 = line1.replace("SBKCCHAR1","").replace("&","and").replace("'","").replace("`","").replace('"',"").replace("|","")
                    cleaned_line2 = line2.replace("Ads","www").replace("SBKCCHAR1","").replace("&","and").replace("'","").replace("`","").replace('"',"").replace("|","")
                    t = str(line3).replace(".","").replace(" ","")
                    if t.isdigit():
                        cleaned_line3 = line3.replace("SBKCCHAR1","").replace("&","and").replace("'","").replace("`","").replace('"',"").replace("|","")
                    else:
                        cleaned_line3 = line3.replace("SBKCCHAR1","").replace("&","and").replace("'","").replace("`","").replace('"',"").replace("|","")
                    cleaned_line3 = cleaned_line3.replace(" My Ad Centre","")

                    # Log cleaned strings
                    with open(os.path.join(tmpfolder, "semlogging.txt"), 'a') as f:
                        f.write(f'line1 = {cleaned_line1}\n')
                        f.write(f'line2 = {cleaned_line2}\n')
                        f.write(f'line3 = {cleaned_line3}\n')

                    # Process HTML template
                    with open(os.path.join(srvfolder, "searchtemplate.html"), "r", encoding="utf-8") as f:
                        html_template = f.read()

                    # Replace placeholders with cleaned values
                    html_filled = html_template.replace("SEMRESULTSLINE1", cleaned_line1)\
                                            .replace("SEMRESULTSLINE2", cleaned_line2)\
                                            .replace("SEMRESULTSLINE3", cleaned_line3)

                    # Save filled HTML
                    working_html_path = os.path.join(srvfolder, "working.html")
                    with open(working_html_path, "w", encoding="utf-8") as f:
                        f.write(html_filled)
                    from datetime import datetime
                    # Save timestamped debug HTML (optional but helpful)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    html_path = os.path.join(srvfolder, f"{timestamp}_{id}.html")
                    with open(html_path, "w", encoding="utf-8") as f:
                        f.write(html_filled)

                    # Generate unique filename and hash
                    fname = uuid.uuid4()
                    amd5 = md5(working_html_path)

                    # Render image using wkhtmltoimage
                    try:
                        png_path = os.path.join(documentroot, creativesfolder, f"{fname}.png")
                        pcall = f"xvfb-run -a wkhtmltoimage {working_html_path} {png_path}"
                        p = subprocess.Popen(pcall, shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
                        p.communicate()
                        sleep(1)

                        if os.path.exists(png_path):
                            size = os.path.getsize(png_path)
                            logger.info(f"Image created: {png_path} | Size: {size} bytes")
                            if size < 5000:
                                logger.warning("Image might be blank (size < 5KB)")
                        else:
                            logger.error(f"Image not found: {png_path}")

                    except Exception as e:
                        logger.error(f"Failed to save photo error: {str(e)}")
                    
                    query = ("insert into panel.semdetails(sem_id, ad_md5, sem_headline, sem_url, sem_description, sem_search_key) values (%s,%s,%s,%s,%s,%s);")
                    cursor2.execute(query, (str(id), str(amd5), str(addslashes(line2)), str(addslashes(line1)), str(addslashes(line3)), str(addslashes(sem_search_key))))
                    cnx2.commit()

                    query = (f"insert into panel.tmpimportdigital(digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url, creative_path, ad_md5, site_url, site_domain, panelist_id, file_type) values ('Search Engine Marketing', '{pemail}', {str(id)}, '{str(ts)}', 0, 'SEARCH', '', '{user_ip}', '{line2}', '', 'google.com/search', '{siteurl+creativesfolder+str(fname)}.png', '{amd5}', 'https://www.google.com/search', 'google.com', 0, 'PNG');")
                    cursor2.execute(query)
                    cnx2.commit()

    query = ("insert into panel.log_cron(cron_step) value ('step 5');")
    cursor.execute(query)
    cnx.commit()
    logger.info('step 5 : Done')


    query = ("update panel.browserdata_sem set state =2 where state =1;")
    cursor.execute(query)
    cnx.commit()

    query = ("insert into panel.log_cron(cron_step) value ('step 7');")
    cursor.execute(query)
    cnx.commit()
    logger.info('step 7 : Done')

except Exception as e:
    tb = traceback.extract_tb(e.__traceback__)[-1]
    error_message = f"error: {e} [Line: {tb.lineno}]"
    logger.error(error_message)
finally:
    if 'cnx' in locals():
        cnx.close()
    if 'cnx2' in locals():
        cnx2.close()
    if 'cnx3' in locals():
        cnx3.close()
