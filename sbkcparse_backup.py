import mysql.connector
import uuid
import urllib
import urllib.request
import socket
import hashlib
from pymediainfo import MediaInfo
from shutil import copyfile
import subprocess
import time
from time import sleep
import sys
import signal
import requests


class DownloadTimeoutError(Exception):
    pass


def timeout(wait_for):
    """
    Extends timeout to a function and raises
    DownloadTimeoutError exception
    """
    def set_alarm(nsecs):
        """ Sets a signal with ALARM signal """
        signal.signal(signal.SIGALRM, handler)
        signal.alarm(nsecs)

    def unset_alarm():
        """ Resets alarm """
        signal.alarm(0)

    def handler(signum, frame):
        unset_alarm()
        raise DownloadTimeoutError("Download request timed out")

    def timeout_decorator(func):

        def wrapper(*args, **kwargs):
            set_alarm(wait_for)
            retval = func(*args, **kwargs)
            unset_alarm()
            return retval
        
        return wrapper
    
    return timeout_decorator

@timeout(wait_for=30)
def download_and_save_file(url: str, fname: str):
    """
    Downloads contents of url and save it into file
    """
    try:
        print('Fname: ', fname)
        response = requests.get(url, allow_redirects=True)
        print('Status code: ', response.status_code)
        
        if response.status_code == 200:
            print('Writing bytes: ', len(response.content))
            if len(response.content) > 0:
                print('Saving downloaded content to: ', fname)
                with open(fname, 'wb') as fh:
                    fh.write(response.content)
    except Exception as ex:
        print(ex.__class__.__name__, str(ex))

PYTHONIOENCODING="UTF-8"
def remove_non_ascii_1(text):
    return ''.join([i if ord(i) < 128 else ' ' for i in text])

def md5(fname):
    hash_md5 = hashlib.md5()
    with open(fname, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def md5_latest(fname):
    ts = str(int(round(time.time())))
    hash_md5 = hashlib.md5(ts.encode())
    with open(fname, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def get_info(domain):
    req = urllib.request.Request(str(domain), headers={ 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.47 Safari/537.36'})
    try:
        response = urllib.request.urlopen(req)
        return response.read().decode('utf-8')
    except urllib.error.HTTPError as e:
        print('PRINT HTTPError', e.code, e.reason)
    except urllib.error.URLError as e:
        print('PRINT URLError', e.reason)
    except Exception as err:
        print(err)

def addslashes(s):
    if s != None:
        l = ["\\", '"', "'", "\0", ]
        for i in l:
            if i in s:
                s = s.replace(i, '\\'+i)
    return s

hostname=socket.gethostname()
print(hostname)
if hostname=='nmg-hp-250':
    username='root'
    pwd='root'
    dbname='panel'
    creativesfolder='creatives/'
    documentroot='/var/www/html/'
    siteurl='http://localhost/mobile-digital/'
    localfolder='mobile-digital/'
    srvfolder=str(documentroot+localfolder)
    srvfolder=str(documentroot+localfolder)
    tmpfolder=srvfolder+'tmp/'

else:
    #####  for server
    username='panel'
    pwd='U6aR8LxX5fM2vM9l^'
    dbname='panel'
    creativesfolder='creatives/'
    documentroot='/home/<USER>/WorkSpace/SBKC_Parser/var/www/html/'
    #siteurl='http://collect.sbkcenter.com/'
    siteurl='https://files-collect-sbkcenter-com.s3.us-west-2.amazonaws.com/uat/'
    localfolder=''
    srvfolder='/home/<USER>/WorkSpace/SBKC_Parser/srv/parse/'
    tmpfolder='/home/<USER>/WorkSpace/SBKC_Parser/tmp/'
    hostname='collectdb.uat.sbkcenter.com'




cnx = mysql.connector.connect(user=username, password=pwd, database=dbname , host=hostname)
cursor = cnx.cursor()

cnx2 = mysql.connector.connect(user=username, password=pwd, database=dbname , host=hostname)
cursor2 = cnx2.cursor()

cnx3 = mysql.connector.connect(user=username, password=pwd, database=dbname , host=hostname)
cursor3 = cnx3.cursor()

query = ("delete from panel.browserdata where state >0;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 1');")
cursor.execute(query)
cnx.commit()
print('step1',query)

query = ("update panel.browserdata set state =1 where state =0;")
cursor.execute(query)
cnx.commit()
try:
    query = ("INSERT INTO panel.browserdata_sem SELECT * FROM browserdata WHERE event_type = 'SEARCH' AND sem_search_key != '';")
    cursor.execute(query)
    cnx.commit()
except Exception as e:
    print(str(e))


query = ("delete  FROM panel.browserdata WHERE event_type = 'SEARCH' AND sem_search_key = 'null';")
cursor.execute(query)
cnx.commit()


query = ("insert into panel.panelist_ip(ip, pemail) select distinct(ip) as ip, '' as pemail from panel.android_app_data where pemail like '%@%' and ip not in (select ip from panel.panelist_ip) and ip <> '*************';")
cursor.execute(query)
cnx.commit()

query = ("update panel.panelist_ip a inner join panel.android_app_data b on a.ip=b.ip set a.pemail = b.pemail where a.pemail = '';")
cursor.execute(query)
cnx.commit()

query = ("update panel.browserdata a inner join panel.panelist_ip b on a.user_ip=b.ip set a.pemail = b.pemail where a.event_type = 'MOBILE' and a.pemail is null;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 2');")
cursor.execute(query)
cnx.commit()
print('step2',query)

query = ("DELETE FROM panel.tmpimportdigital WHERE status=3;")
cursor.execute(query)
cnx.commit()

query = ("INSERT INTO panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url) SELECT 'Online Display' as digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url FROM panel.browserdata WHERE request_url LIKE '%tpc.goog%simgad%' AND request_url NOT LIKE '%daca%' AND request_url NOT LIKE '%html%' AND event_type = 'RESOURCE' AND state =1 AND pemail LIKE '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("INSERT INTO panel.tmpimportdigital( digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url ) SELECT 'Online Display' AS digital_channel, pemail, id, ts, user_id, event_type, event_id, user_ip, payload, tab_id, request_url FROM panel.browserdata WHERE request_url LIKE '%2mdn%' AND payload NOT LIKE '%2mdn%' AND request_url NOT LIKE '%html%' AND request_url NOT LIKE '%.js' AND request_url NOT LIKE '%.mp4' AND request_url NOT LIKE '%1x1%' AND request_url NOT LIKE '%dot.gif%' AND request_url NOT LIKE '%richmedia%' AND request_url NOT LIKE '%videoplayback%' AND request_url NOT LIKE '%.webm' AND request_url NOT LIKE '%.swf%' AND event_type = 'RESOURCE' AND state = 1 AND pemail LIKE '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%cdn.adlegend.com%.html%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url ) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%ad.atdmt.com/as/direct%.html%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url ) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%cdn.flashtalking.com%.html%' and request_url not like '%index.html%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like 'https://tpc.googlesyndication.com/%.html%' and request_url not like '%index.html%' and request_url not like '%gadget%' and request_url not like '%container%' and request_url not like '%sodar%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , replace(request_url,'?e=69','') as request_url from panel.browserdata where request_url like '%s0.2mdn.net/%.html%' and payload not like '%2mdn%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Online Display' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where (request_url like '%300x600%.html%' or request_url like '%728x90%.html%' or request_url like '%300x250%.html%' or request_url like '%320x50%.html%' or request_url like '%300x50%.html%' or request_url like '%160x600%.html%' or request_url like '%970x250%.html%' or request_url like '%970x90%.html%' ) and request_url not like '%cdn.adlegend.com%' and request_url not like '%atdmt%' and request_url not like '%flashtalking%' and request_url not like '%googlesynd%' and request_url not like '%2mdn.net%' and request_url not like '%doubleclick%' and event_type = 'RESOURCE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)

query = ("insert into panel.tmpimportdigital (digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select 'Mobile' as digital_channel, pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.browserdata where request_url like '%2mdn%' and payload not like '%2mdn%' and request_url not like '%html%' and request_url not like '%.js' and request_url not like '%.mp4' and request_url not like '%1x1%' and request_url not like '%dot.gif%' and request_url not like '%richmedia%' and request_url not like '%videoplayback%' and request_url not like '%.webm' and request_url not like '%.swf%' and event_type = 'MOBILE' and state =1 and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = (" insert into panel.tmpimportdigital (digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url) select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%tpc.goog%simgad%' and request_url not like '%daca%'  and request_url not like '%html%'  and event_type = 'MOBILE' and state =1 and pemail like '%@%';")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url )   select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%cdn.adlegend.com%.html%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%'  ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%ad.atdmt.com/as/direct%.html%' and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)   select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like '%cdn.flashtalking.com%.html%' and request_url not like '%index.html%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital (digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where request_url like 'https://tpc.googlesyndication.com/%.html%' and request_url not like '%index.html%' and  request_url not like '%gadget%' and  request_url not like '%container%'  and  request_url not like '%sodar%'    and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , replace(request_url,'?e=69','') as request_url   from panel.browserdata where request_url like '%s0.2mdn.net/%.html%' and payload not like '%2mdn%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%' ;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url)  select  'Mobile' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where (request_url like '%300x600%.html%' or request_url like '%728x90%.html%' or request_url like '%300x250%.html%' or request_url like '%320x50%.html%' or request_url like '%300x50%.html%' or request_url like '%160x600%.html%' or request_url like '%970x250%.html%' or request_url like '%970x90%.html%' ) and request_url not like '%cdn.adlegend.com%'  and request_url not like '%atdmt%'   and request_url not like '%flashtalking%'   and request_url not like '%googlesynd%'   and request_url not like '%2mdn.net%' and request_url not like '%doubleclick%'  and event_type = 'MOBILE' and state =1  and pemail like '%@%';")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url) select 'Online Video' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where (request_url like '%innovid.com%mp4' or request_url like '%ads.eyeviewads.com%mp4' or request_url like '%tribalfusion.com%mp4' or request_url like '%2mdn.net/videoplayback/%file.mp4') and event_type = 'RESOURCE'  and state =1  and pemail like '%@%' ; ")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.tmpimportdigital ( digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id,request_url) select 'Mobile Video' as digital_channel,   pemail, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url   from panel.browserdata where (request_url like '%innovid.com%mp4' or request_url like '%ads.eyeviewads.com%mp4' or request_url like '%tribalfusion.com%mp4' or request_url like '%2mdn.net/videoplayback/%file.mp4') and event_type = 'MOBILE'  and state =1  and pemail like '%@%' ; ")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 3');")
cursor.execute(query)
cnx.commit()

query = ("select  digital_channel,   id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.tmpimportdigital  where request_url not like '%html%' order by id;")
cursor.execute(query)



for ( digital_channel, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url ) in cursor:
	
    fname = uuid.uuid4()
    fname= str(fname)
    try:
        print('Downloading and saving: ', request_url)
        download_and_save_file(
            url=request_url,
            fname=str(documentroot+localfolder+creativesfolder) + str(fname)
        )
    except:
        e = sys.exc_info()[0]
        continue

    try:
        amd5 = md5(str(documentroot+localfolder+creativesfolder)+ str(fname))
        query = ("update panel.tmpimportdigital set  creative_path = '"+ str(siteurl+creativesfolder) + str(fname)  + "' , ad_md5 = '" + str(amd5)  + "' where id = " + str(id) + ";")
        cursor2.execute(query)
        cnx2.commit()

        query = ("select cast(payload as char(255)) from panel.browserdata where event_type = 'URL' and user_ip = '" + str(user_ip.replace('::','')) + "' and id < " + str(id)  + "  and payload not like '%chrome%' order by id desc  limit 1;")
        cursor2.execute(query)
        row = cursor2.fetchone()
        if cursor2.rowcount > 0:
            query = ("update panel.tmpimportdigital set site_url = '" + str(row[0])  + "' where id = " + str(id) + "  ;")
            cursor3.execute(query)
            cnx3.commit()
        media_info = MediaInfo.parse(str(documentroot+localfolder+creativesfolder) + str(fname))
        x=0
        for track in media_info.tracks:
            if x < 1:
                query = ("update panel.tmpimportdigital set file_type = '" + str(track.codec)  + "' where id = " + str(id) + ";")
                cursor3.execute(query)
                cnx3.commit()
            x=1
    except Exception as e: 
        print("Some issue in file", e)
        e = sys.exc_info()[0]

query = ("select  digital_channel,   id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url from panel.tmpimportdigital  where request_url like '%html%';")
cursor.execute(query)

for ( digital_channel, id, ts, user_id , event_type , event_id , user_ip , payload , tab_id , request_url ) in cursor:
    try:
        fname = uuid.uuid4()
        copyfile(str(srvfolder)+"HTML5template.html", str(documentroot+localfolder+creativesfolder) + str(fname) + ".html")
        cH = 400
        cW = 400

        if "300x600" in str(request_url):
            cW = 300
            cH = 600
        if "728x90" in str(request_url):
            cW = 728
            cH = 90
        if "300x250" in str(request_url):
            cW = 300
            cH = 250
        if "320x50" in str(request_url):
            cW = 320
            cH = 50
        if "300x50" in str(request_url):
            cW = 300
            cH = 50
        if "160x600" in str(request_url):
            cW = 160
            cH = 600
        if "970x250" in str(request_url):
            cW = 970
            cH = 250
        if "970x90" in str(request_url):
            cW = 970
            cH = 90

        pcall = "sed -i 's|SBKCWIDTH|" + str(cW)  + "|g' "+str(documentroot+localfolder+creativesfolder) + str(fname) + ".html"
        p=subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)

        sleep(1)
        pcall = "sed -i 's|SBKCHEIGHT|" + str(cH)  + "|g' "+str(documentroot+localfolder+creativesfolder) + str(fname) + ".html"
        p=subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
        sleep(1)
        pcall = "sed -i 's|SBKCSRC|" + str(request_url)  + "|g' "+str(documentroot+localfolder+creativesfolder) + str(fname) + ".html"
        p=subprocess.Popen([pcall], shell=True, stdin=None, stdout=None, stderr=None, close_fds=True)
        sleep(1)
        amd5 = md5( str(documentroot+localfolder+creativesfolder)+ str(fname) + ".html")
        query = ("update panel.tmpimportdigital set  creative_path = '"+str(siteurl+creativesfolder) + str(fname)  + ".html' , ad_md5 = '" + str(amd5)  + "' where id = " + str(id) + ";")
        cursor2.execute(query)
        cnx2.commit()
        query = ("select cast(payload as char(255)) from panel.browserdata where event_type = 'URL' and user_ip = '" + user_ip + "' and id < " + str(id)  + "  and payload not like '%chrome%' order by id desc  limit 1;")
        cursor2.execute(query)
        row = cursor2.fetchone()
        if cursor2.rowcount > 0:
            query = ("update panel.tmpimportdigital set site_url = '" + str(row[0])  + "' where id = " + str(id) + "  ;")
            cursor3.execute(query)
            cnx3.commit()
        if cW > 0:
            query = ("update panel.tmpimportdigital set file_type = 'HTML' where id = " + str(id) + ";")
            cursor3.execute(query)
            cnx3.commit()
    except: 
        e = sys.exc_info()[0]

query = ("insert into panel.log_cron(cron_step) value ('step 4');")
cursor.execute(query)
cnx.commit()
print('step4',query)
query = ("delete from panel.tmpimportdigital where digital_channel =  'Online Display' and (file_type is null OR file_type = ''); ")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 5');")
cursor.execute(query)
cnx.commit()
print('step 5',query )

query = (" update  panel.tmpimportdigital set site_domain =  LEFT(SUBSTRING_INDEX((SUBSTRING_INDEX((SUBSTRING_INDEX(replace(site_url, 'https://', 'http://'), 'http://', -1)), '/', 1)), '.', -2),100) ; ")
cursor.execute(query)
cnx.commit()


query = ("insert into  panel.tmpimportdigitalnewcreative(ad_md5)  select distinct(ad_md5) as ad_md5 from panel.tmpimportdigital where ad_md5 not in (select ad_md5 from panel.tmpuniqueads);")
cursor.execute(query)
cnx.commit()

query = ("update panel.tmpimportdigitalnewcreative  a inner join panel.tmpimportdigital b on a.ad_md5=b.ad_md5 set a.creative_path=b.creative_path, a.digital_channel=b.digital_channel, a.file_type=b.file_type ;")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpimportdigitalnewcreative   where creative_path is null;")
cursor.execute(query)
cnx.commit()

query = ("update panel.browserdata set state =2 where state =1;")
cursor.execute(query)
cnx.commit()


query = ("drop table if exists panel.tmpurlimport;")
cursor.execute(query)
cnx.commit()


query = ("create table panel.tmpurlimport as  select * from panel.browserdata  where event_type = 'URL' and state =2; ")
cursor.execute(query)
cnx.commit()

query = ("ALTER TABLE tmpurlimport ADD PRIMARY KEY(id),ADD INDEX user_ip (user_ip)")
cursor.execute(query)
cnx.commit()


query = ("alter table panel.tmpurlimport add column simple_domain varchar(100); ")
cursor.execute(query)
cnx.commit()

query = ("update  panel.tmpurlimport set simple_domain =  left(SUBSTRING_INDEX((SUBSTRING_INDEX((SUBSTRING_INDEX(replace(payload, 'https://', 'http://'), 'http://', -1)), '/', 1)), '.', -2) ,100); ")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpurlimport where simple_domain ='' or simple_domain is null; ")
cursor.execute(query)
cnx.commit()

query = ("drop table if exists panel.tmpurlsummary;  ")
cursor.execute(query)
cnx.commit()

query = ("create table panel.tmpurlsummary as select pemail, payload, user_ip, min(id) as id from panel.tmpurlimport group by  pemail, payload, user_ip; ")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpurlsummary where payload like '%chrome://%'; ")
cursor.execute(query)
cnx.commit()

query = ("alter table panel.tmpurlsummary add column site_url varchar(300), add column simple_domain varchar(100) ; ")
cursor.execute(query)
cnx.commit()

query = ("update panel.tmpurlsummary  set site_url  = left(payload,300); ")
cursor.execute(query)
cnx.commit()

query = ("update panel.tmpurlsummary  set simple_domain =  left(SUBSTRING_INDEX((SUBSTRING_INDEX((SUBSTRING_INDEX(replace(payload, 'https://', 'http://'), 'http://', -1)), '/', 1)), '.', -2) ,100);  ")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.tmpurlimport where id not in (select id from panel.tmpurlsummary); ")
cursor.execute(query)
cnx.commit()

query = ("delete from panel.browserdata where state =2;")
cursor.execute(query)
cnx.commit()

query = ("insert into panel.log_cron(cron_step) value ('step 6');")
cursor.execute(query)
cnx.commit()
print('step 6',query )

query = ("insert into panel.cron_sequence(execution) value ('1');")
cursor.execute(query)
cnx.commit()

cnx.close()
cnx2.close()
cnx3.close()



