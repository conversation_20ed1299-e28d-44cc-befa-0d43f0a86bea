import undetected_chromedriver as uc
import logging

logging.basicConfig(level=logging.INFO, filename='logs/sem_process.log', format='%(asctime)s %(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

try:
    options = uc.ChromeOptions()
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-infobars')
    options.add_argument('--disable-notifications')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--no-sandbox')
    options.add_argument('--headless=new')
    options.add_argument('--window-size=1920,1080')
    logger.info("Creating ChromeDriver")
    driver = uc.Chrome(options=options, use_subprocess=True, version_main=133)
    logger.info(f"Driver created successfully at: {driver.service.path}")
    driver.get("https://www.google.com/search?q=best+python+course")
    logger.info(f"Navigated to Google, title: {driver.title}")
    page_source = driver.page_source
    with open("abc.html", 'w', encoding='utf-8') as f:
        f.write(page_source)
    driver.quit()
    logger.info("Driver closed")
except Exception as e:
    logger.error(f"Error: {str(e)}", exc_info=True)